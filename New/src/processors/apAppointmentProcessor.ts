import { getDb } from "@database";
import { appointment, patient } from "@database/schema";
import type { GetAPAppointmentType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generateAppointmentBuffer<PERSON>ey,
} from "@utils/bufferManager";
import { logError, logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { ccClient } from "../api";
import { determineAppointmentSyncAction } from "../helpers/contextAwareSync";
import { executeAppointmentSync } from "../helpers/syncActions";

/**
 * Processes AP appointment creation events and syncs to CC with context-aware patient validation
 * This handles AP → CC data flow with intelligent sync decisions
 *
 * **Context-Aware Logic:**
 * - Validates patient exists and is synced to CC before appointment sync
 * - Uses intelligent sync decisions to prevent unnecessary API calls
 * - Skips appointment sync if already synced to target platform
 * - Handles patient sync requirements before appointment operations
 *
 * @param payload - AP appointment data from webhook
 * @param context - Webhook processing context
 * @returns Processing result with context-aware sync status
 */
export async function processAPAppointmentCreate(
	payload: GetAPAppointmentType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP appointment create for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAPAppointmentCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP appointment creation recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Check if appointment already exists in our database
		const existingAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.apId, payload.id))
			.limit(1);

		if (existingAppointment.length > 0) {
			console.log(
				`Appointment already exists in CC, ID: ${existingAppointment[0].ccId}`,
			);
			return {
				success: true,
				message: `Appointment already exists in CC. CC ID: ${existingAppointment[0].ccId}`,
			};
		}

		// Find the patient this appointment belongs to
		const dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, payload.contactId))
			.limit(1)
			.then((results) => results[0]);

		if (!dbPatient) {
			const message = `Patient not found for AP contact ID: ${payload.contactId}. Cannot create appointment.`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// NEW: Use context-aware appointment sync logic
		const appointmentSyncContext = determineAppointmentSyncAction(
			dbPatient,
			existingAppointment.length > 0 ? existingAppointment[0] : null,
			'ap',
			'create'
		);

		console.log(`🎯 Appointment sync decision: ${appointmentSyncContext.action} - ${appointmentSyncContext.reason}`);

		// Check if patient sync is required before appointment sync
		if (appointmentSyncContext.patientSyncRequired) {
			const message = `Cannot sync appointment: ${appointmentSyncContext.reason}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Handle skip action (appointment already synced)
		if (appointmentSyncContext.action === 'skip') {
			return {
				success: true,
				message: appointmentSyncContext.reason,
			};
		}

		// Execute context-aware appointment sync
		console.log(`🔄 Executing context-aware appointment sync for AP ID: ${payload.id}`);
		const syncResult = await executeAppointmentSync(
			dbPatient,
			existingAppointment.length > 0 ? existingAppointment[0] : null,
			payload,
			appointmentSyncContext.action,
			'cc'
		);
		console.log(`🔄 Context-aware appointment sync result:`, syncResult);

		// Handle sync result
		if (!syncResult.success) {
			await logSyncError(
				"AP_APPOINTMENT_CREATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"AP",
				"APAppointmentProcessor",
			);
			return {
				success: false,
				message: `Failed to sync appointment to CC: ${syncResult.message}`,
			};
		}

		// Store appointment in database with sync results
		const appointmentData: any = {
			apId: payload.id,
			patientId: dbPatient.id,
			apData: payload,
			apUpdatedAt: new Date(), // AP appointments don't have timestamp fields
		};

		// Add CC data if sync was successful
		if (syncResult.data?.ccAppointment) {
			appointmentData.ccId = syncResult.recordId;
			appointmentData.ccData = syncResult.data.ccAppointment;
			appointmentData.ccUpdatedAt = new Date(syncResult.data.ccAppointment.updatedAt || syncResult.data.ccAppointment.createdAt);
		}

		await db.insert(appointment).values(appointmentData);

		console.log(
			`Appointment sync completed successfully. AP ID: ${payload.id}, Result: ${syncResult.message}`,
		);

		return {
			success: true,
			message: syncResult.message,
		};
	} catch (error) {
		const message = `Error processing AP appointment create: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_APPOINTMENT_CREATE_ERROR",
			error,
			{
				apAppointmentId: payload.id,
				requestId: context.requestId,
			},
			"APAppointmentProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP appointment update events and syncs to CC
 * This handles AP → CC data flow
 *
 * @param payload - AP appointment data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPAppointmentUpdate(
	payload: GetAPAppointmentType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP appointment update for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAPAppointmentUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP appointment update recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Find appointment in database
		const dbAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.apId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbAppointment) {
			// If appointment doesn't exist, create it
			console.log(
				`Appointment not found, creating new appointment for AP ID: ${payload.id}`,
			);
			return await processAPAppointmentCreate(payload, context);
		}

		if (!dbAppointment.ccId) {
			const message = `Appointment exists but not synced to CC yet. AP ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Update appointment in CC
		const ccAppointmentData = {
			startTime: payload.startTime,
			endTime: payload.endTime,
			title: payload.title || "Appointment from AP",
			notes: "", // AP appointments don't have notes field
			status: payload.appointmentStatus || "scheduled",
		};

		const ccAppointment = await ccClient.appointment.update(
			dbAppointment.ccId,
			ccAppointmentData,
		);

		if (!ccAppointment) {
			const message = `Failed to update appointment in CC for AP ID: ${payload.id}`;
			await logSyncError(
				"AP_TO_CC_APPOINTMENT_UPDATE_FAILED",
				message,
				payload.id,
				"AP",
				"APAppointmentProcessor",
			);
			return {
				success: false,
				message,
			};
		}

		// Update appointment in database
		await db
			.update(appointment)
			.set({
				apData: payload,
				ccData: ccAppointment,
				apUpdatedAt: new Date(), // AP appointments don't have timestamp fields
				ccUpdatedAt: new Date(
					ccAppointment.updatedAt || ccAppointment.createdAt,
				),
			})
			.where(eq(appointment.id, dbAppointment.id));

		console.log(
			`Successfully updated appointment in CC. CC ID: ${ccAppointment.id}`,
		);

		return {
			success: true,
			message: `Appointment updated successfully in CC. CC ID: ${ccAppointment.id}`,
		};
	} catch (error) {
		const message = `Error processing AP appointment update: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_APPOINTMENT_UPDATE_ERROR",
			error,
			{
				apAppointmentId: payload.id,
				requestId: context.requestId,
			},
			"APAppointmentProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP appointment deletion events and syncs to CC
 * This handles AP → CC data flow
 *
 * @param payload - AP appointment data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPAppointmentDelete(
	payload: GetAPAppointmentType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP appointment delete for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAPAppointmentDelete",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP appointment deletion recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Find appointment in database
		const dbAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.apId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbAppointment) {
			console.log(`Appointment not found for deletion, AP ID: ${payload.id}`);
			return {
				success: true,
				message: `Appointment not found, nothing to delete. AP ID: ${payload.id}`,
			};
		}

		if (dbAppointment.ccId) {
			// Delete appointment from CC
			try {
				await ccClient.appointment.delete(dbAppointment.ccId);
				console.log(
					`Successfully deleted appointment from CC. CC ID: ${dbAppointment.ccId}`,
				);
			} catch (error) {
				const message = `Failed to delete appointment from CC for AP ID: ${payload.id}`;
				await logSyncError(
					"AP_TO_CC_APPOINTMENT_DELETE_FAILED",
					error,
					payload.id,
					"AP",
					"APAppointmentProcessor",
				);
				return {
					success: false,
					message,
				};
			}
		}

		// Remove appointment from database
		await db.delete(appointment).where(eq(appointment.id, dbAppointment.id));

		return {
			success: true,
			message: `Appointment deleted successfully from CC. AP ID: ${payload.id}`,
		};
	} catch (error) {
		const message = `Error processing AP appointment delete: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_APPOINTMENT_DELETE_ERROR",
			error,
			{
				apAppointmentId: payload.id,
				requestId: context.requestId,
			},
			"APAppointmentProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Creates an appointment in CC
 *
 * @param ccPatientId - CC patient ID
 * @param apAppointment - AP appointment data
 * @returns Result of CC appointment creation
 */
export async function createCCAppointment(
	ccPatientId: string,
	apAppointment: GetAPAppointmentType,
): Promise<{
	success: boolean;
	message: string;
	ccAppointment?: any;
}> {
	try {
		const appointmentData = {
			patientId: ccPatientId,
			startTime: apAppointment.startTime,
			endTime: apAppointment.endTime,
			title: apAppointment.title || "Appointment from AP",
			notes: "", // AP appointments don't have notes field
			status: apAppointment.appointmentStatus || "scheduled",
		};

		const ccAppointment = await ccClient.appointment.create(appointmentData);

		return {
			success: true,
			message: "Appointment created successfully in CC",
			ccAppointment,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to create CC appointment:`, error);

		return {
			success: false,
			message: `Failed to create CC appointment: ${errorMessage}`,
		};
	}
}
